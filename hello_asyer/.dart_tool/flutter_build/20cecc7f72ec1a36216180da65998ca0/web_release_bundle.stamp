{"inputs": ["/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/pubspec.yaml", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/.dart_tool/flutter_build/20cecc7f72ec1a36216180da65998ca0/main.dart.js", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/pubspec.yaml", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/Users/<USER>/Documents/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Documents/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "/Users/<USER>/Documents/flutter/bin/cache/pkg/sky_engine/LICENSE", "/Users/<USER>/Documents/flutter/packages/flutter/LICENSE", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/web/index.html", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/web/favicon.png", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/web/icons/Icon-192.png", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/web/icons/Icon-maskable-192.png", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/web/icons/Icon-maskable-512.png", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/web/icons/Icon-512.png", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/web/manifest.json"], "outputs": ["/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/main.dart.js", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/assets/shaders/ink_sparkle.frag", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/assets/AssetManifest.json", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/assets/AssetManifest.bin", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/assets/AssetManifest.bin.json", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/assets/FontManifest.json", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/assets/NOTICES", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/favicon.png", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/icons/Icon-192.png", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/icons/Icon-maskable-192.png", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/icons/Icon-maskable-512.png", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/icons/Icon-512.png", "/Users/<USER>/Documents/augment-projects/asygame/hello_asyer/build/web/manifest.json"]}